# Store Comments Reviews
API to fetch all the comments and reviews of the store and its products



## APIs

### Fetch Store Comments Reviews
```bash
curl --location '{base_url}/lean/get_store_comments/?limit=100&offset=0&visitor_reference=U1719579800140&entity_reference=S1721930951916&comment_types=EXTERNAL_REVIEW%2CCOMMENT%2CREVIEW%2CQUESTION' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'

```

Response:
success response:
```json
{
    "message": "success",
    "data": [
        {
            "reference": "CO202506191659154420",
            "comment_text": "Product ki comment",
            "created_date": "2025-06-19 16:59:15.073557+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 1,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "COMMENT",
            "main_parent_id": "P1749741540045342ZYDK",
            "commenter_reference": "S1721930951916",
            "user_reference": null,
            "store_reference": "S1721930951916",
            "tagged_references_json": [],
            "tagged_users_count": 0,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": null,
            "reviewed_reference_json": null,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT"
        },
        {
            "reference": "CO202506191658520052",
            "comment_text": "Post ki comment",
            "created_date": "2025-06-19 16:58:52.505872+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 1,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": 4.5,
            "level": 1,
            "comment_type": "EXTERNAL_REVIEW",
            "main_parent_id": "PO202506172127562444",
            "commenter_reference": "S1721930951916",
            "user_reference": null,
            "store_reference": "S1721930951916",
            "tagged_references_json": [],
            "tagged_users_count": 0,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": "external review @kitten_kreeps/digene-medicine",
            "reviewed_reference_json": [
                {
                    "type": "PRODUCT",
                    "order": 1,
                    "reference": "P1748335610278489ULRX"
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT"
        },
        {
            "reference": "CO202506181839542146",
            "comment_text": "Su Good to be here as a reviewer. What is even better is i got it only after purchase. ",
            "created_date": "2025-06-18 18:39:54.342833+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": 3.5,
            "level": 1,
            "comment_type": "REVIEW",
            "main_parent_id": "PO202506172016488031",
            "commenter_reference": "U1719579800140",
            "user_reference": "U1719579800140",
            "store_reference": null,
            "tagged_references_json": [],
            "tagged_users_count": 0,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": "verified review @kitten_kreeps/digene-medicine",
            "reviewed_reference_json": [
                {
                    "type": "PRODUCT",
                    "order": 1,
                    "reference": "P1748335610278489ULRX"
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT"
        },
        {
            "reference": "CO202506181616496836",
            "comment_text": "Or just a fake review",
            "created_date": "2025-06-18 16:16:49.506947+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 1,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "QUESTION",
            "main_parent_id": "PO202506172127562444",
            "commenter_reference": "U1719579800140",
            "user_reference": "U1719579800140",
            "store_reference": null,
            "tagged_references_json": [],
            "tagged_users_count": 0,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": null,
            "reviewed_reference_json": null,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT"
        }
    ]
}
```
failure response:
```json
{
    "message": "Invalid comment type"
}
```

